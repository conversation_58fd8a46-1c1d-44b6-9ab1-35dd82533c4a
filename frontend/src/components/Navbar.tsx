import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { LogOut, Store, Package, ShoppingCart, BarChart3 } from "lucide-react";

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  return (
    <nav className="bg-white bg-opacity-80 backdrop-blur-md border-b border-gray-200 border-opacity-50 shadow-elegant sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link
            to="/"
            className="text-2xl font-bold bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent hover:from-primary-700 hover:to-accent-700 transition-all duration-200"
          >
            E-commerce Hub
          </Link>

          <div className="flex items-center space-x-6">
            {user ? (
              <>
                <Link
                  to="/dashboard"
                  className="flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors duration-200 font-medium"
                >
                  <BarChart3 size={20} />
                  <span>Dashboard</span>
                </Link>
                <Link
                  to="/stores"
                  className="flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors duration-200 font-medium"
                >
                  <Store size={20} />
                  <span>Stores</span>
                </Link>
                <Link
                  to="/products"
                  className="flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors duration-200 font-medium"
                >
                  <Package size={20} />
                  <span>Products</span>
                </Link>
                <Link
                  to="/orders"
                  className="flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors duration-200 font-medium"
                >
                  <ShoppingCart size={20} />
                  <span>Orders</span>
                </Link>
                <div className="flex items-center space-x-4 ml-4 pl-4 border-l border-gray-200">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-semibold">
                        {user.full_name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <span className="text-gray-700 font-medium">
                      Hello, {user.full_name}
                    </span>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 text-gray-600 hover:text-red-600 transition-colors duration-200 font-medium"
                  >
                    <LogOut size={20} />
                    <span>Logout</span>
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link
                  to="/login"
                  className="text-gray-600 hover:text-primary-600 transition-colors duration-200 font-medium"
                >
                  Login
                </Link>
                <Link to="/register" className="btn-primary">
                  Get Started
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
