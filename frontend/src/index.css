@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
  }

  body {
    @apply antialiased;
  }
}

@layer components {
  .glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card-dark {
    background: rgba(17, 24, 39, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(75, 85, 99, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    @apply text-gray-700 font-medium py-3 px-6 rounded-xl transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-gray-500 focus:ring-opacity-20;
  }

  .input-modern {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    @apply w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500 focus:ring-opacity-20 focus:border-blue-500 transition-all duration-200 placeholder-gray-400;
  }

  .card-modern {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    box-shadow: 0 4px 20px -2px rgba(0, 0, 0, 0.1);
    @apply rounded-2xl p-6 transition-all duration-200 hover:scale-105;
  }
}
