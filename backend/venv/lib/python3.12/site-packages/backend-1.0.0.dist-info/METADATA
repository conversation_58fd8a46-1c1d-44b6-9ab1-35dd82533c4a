Metadata-Version: 2.4
Name: backend
Version: 1.0.0
Summary: E-commerce Integration Hub Backend API
Project-URL: Homepage, https://github.com/your-org/ecommerce-hub
Project-URL: Documentation, https://github.com/your-org/ecommerce-hub/docs
Project-URL: Repository, https://github.com/your-org/ecommerce-hub.git
Project-URL: Issues, https://github.com/your-org/ecommerce-hub/issues
Author-email: E-commerce Hub Team <<EMAIL>>
License: MIT
Keywords: api,ecommerce,fastapi,shopify,woocommerce
Classifier: Development Status :: 4 - Beta
Classifier: Framework :: FastAPI
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.11
Requires-Dist: alembic>=1.12.1
Requires-Dist: fastapi>=0.104.1
Requires-Dist: httpx>=0.25.2
Requires-Dist: passlib[bcrypt]>=1.7.4
Requires-Dist: psycopg2-binary>=2.9.9
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic[email]>=2.5.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-jose[cryptography]>=3.3.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: sqlalchemy>=2.0.23
Requires-Dist: uvicorn[standard]>=0.24.0
Provides-Extra: all
Requires-Dist: black>=23.0.0; extra == 'all'
Requires-Dist: cloud-sql-python-connector; extra == 'all'
Requires-Dist: flake8>=6.0.0; extra == 'all'
Requires-Dist: google-cloud-secret-manager; extra == 'all'
Requires-Dist: isort>=5.12.0; extra == 'all'
Requires-Dist: mypy>=1.5.0; extra == 'all'
Requires-Dist: pre-commit>=3.4.0; extra == 'all'
Requires-Dist: pytest-asyncio>=0.21.1; extra == 'all'
Requires-Dist: pytest-cov>=4.1.0; extra == 'all'
Requires-Dist: pytest>=7.4.3; extra == 'all'
Requires-Dist: shopifyapi>=12.0.0; extra == 'all'
Requires-Dist: woocommerce>=3.0.0; extra == 'all'
Provides-Extra: dev
Requires-Dist: black>=23.0.0; extra == 'dev'
Requires-Dist: flake8>=6.0.0; extra == 'dev'
Requires-Dist: isort>=5.12.0; extra == 'dev'
Requires-Dist: mypy>=1.5.0; extra == 'dev'
Requires-Dist: pre-commit>=3.4.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.1; extra == 'dev'
Requires-Dist: pytest-cov>=4.1.0; extra == 'dev'
Requires-Dist: pytest>=7.4.3; extra == 'dev'
Provides-Extra: gcp
Requires-Dist: cloud-sql-python-connector; extra == 'gcp'
Requires-Dist: google-cloud-secret-manager; extra == 'gcp'
Provides-Extra: shopify
Requires-Dist: shopifyapi>=12.0.0; extra == 'shopify'
Provides-Extra: woocommerce
Requires-Dist: woocommerce>=3.0.0; extra == 'woocommerce'
