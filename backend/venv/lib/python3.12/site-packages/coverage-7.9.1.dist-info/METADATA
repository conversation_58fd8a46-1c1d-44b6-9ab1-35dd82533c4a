Metadata-Version: 2.4
Name: coverage
Version: 7.9.1
Summary: Code coverage measurement for Python
Home-page: https://github.com/nedbat/coveragepy
Author: <PERSON> and 240 others
Author-email: <EMAIL>
License: Apache-2.0
Project-URL: Documentation, https://coverage.readthedocs.io/en/7.9.1
Project-URL: Funding, https://tidelift.com/subscription/pkg/pypi-coverage?utm_source=pypi-coverage&utm_medium=referral&utm_campaign=pypi
Project-URL: Issues, https://github.com/nedbat/coveragepy/issues
Project-URL: Mastodon, https://hachyderm.io/@coveragepy
Project-URL: Mastodon (nedbat), https://hachyderm.io/@nedbat
Keywords: code coverage testing
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Quality Assurance
Classifier: Topic :: Software Development :: Testing
Classifier: Development Status :: 5 - Production/Stable
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Provides-Extra: toml
Requires-Dist: tomli; python_full_version <= "3.11.0a6" and extra == "toml"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-python
Dynamic: summary

.. Licensed under the Apache License: http://www.apache.org/licenses/LICENSE-2.0
.. For details: https://github.com/nedbat/coveragepy/blob/master/NOTICE.txt

===========
Coverage.py
===========

Code coverage measurement for Python.

.. image:: https://raw.githubusercontent.com/vshymanskyy/StandWithUkraine/main/banner2-direct.svg
    :target: https://vshymanskyy.github.io/StandWithUkraine
    :alt: Stand with Ukraine

-------------

|  |kit| |license| |versions|
|  |test-status| |quality-status| |docs| |metacov|
|  |tidelift| |sponsor| |stars| |mastodon-coveragepy| |mastodon-nedbat|
   |bluesky-nedbat|

Coverage.py measures code coverage, typically during test execution. It uses
the code analysis tools and tracing hooks provided in the Python standard
library to determine which lines are executable, and which have been executed.

Coverage.py runs on these versions of Python:

.. PYVERSIONS

* Python 3.9 through 3.14 beta 2, including free-threading.
* PyPy3 versions 3.9, 3.10, and 3.11.

Documentation is on `Read the Docs`_.  Code repository and issue tracker are on
`GitHub`_.

.. _Read the Docs: https://coverage.readthedocs.io/en/7.9.1/
.. _GitHub: https://github.com/nedbat/coveragepy

**New in 7.x:**
``[run] core`` setting;
``[run] source_dirs`` setting;
``Coverage.branch_stats()``;
multi-line exclusion patterns;
function/class reporting;
experimental support for sys.monitoring;
dropped support for Python 3.7 and 3.8;
added ``Coverage.collect()`` context manager;
improved data combining;
``[run] exclude_also`` setting;
``report --format=``;
type annotations.

**New in 6.x:**
dropped support for Python 2.7, 3.5, and 3.6;
write data on SIGTERM;
added support for 3.10 match/case statements.


For Enterprise
--------------

.. |tideliftlogo| image:: https://nedbatchelder.com/pix/Tidelift_Logo_small.png
   :alt: Tidelift
   :target: https://tidelift.com/subscription/pkg/pypi-coverage?utm_source=pypi-coverage&utm_medium=referral&utm_campaign=readme

.. list-table::
   :widths: 10 100

   * - |tideliftlogo|
     - `Available as part of the Tidelift Subscription. <https://tidelift.com/subscription/pkg/pypi-coverage?utm_source=pypi-coverage&utm_medium=referral&utm_campaign=readme>`_
       Coverage and thousands of other packages are working with
       Tidelift to deliver one enterprise subscription that covers all of the open
       source you use.  If you want the flexibility of open source and the confidence
       of commercial-grade software, this is for you.
       `Learn more. <https://tidelift.com/subscription/pkg/pypi-coverage?utm_source=pypi-coverage&utm_medium=referral&utm_campaign=readme>`_


Getting Started
---------------

Looking to run ``coverage`` on your test suite? See the `Quick Start section`_
of the docs.

.. _Quick Start section: https://coverage.readthedocs.io/en/7.9.1/#quick-start


Change history
--------------

The complete history of changes is on the `change history page`_.

.. _change history page: https://coverage.readthedocs.io/en/7.9.1/changes.html


Code of Conduct
---------------

Everyone participating in the coverage.py project is expected to treat other
people with respect and to follow the guidelines articulated in the `Python
Community Code of Conduct`_.

.. _Python Community Code of Conduct: https://www.python.org/psf/codeofconduct/


Contributing
------------

Found a bug? Want to help improve the code or documentation? See the
`Contributing section`_ of the docs.

.. _Contributing section: https://coverage.readthedocs.io/en/7.9.1/contributing.html


Security
--------

To report a security vulnerability, please use the `Tidelift security
contact`_.  Tidelift will coordinate the fix and disclosure.

.. _Tidelift security contact: https://tidelift.com/security


License
-------

Licensed under the `Apache 2.0 License`_.  For details, see `NOTICE.txt`_.

.. _Apache 2.0 License: http://www.apache.org/licenses/LICENSE-2.0
.. _NOTICE.txt: https://github.com/nedbat/coveragepy/blob/master/NOTICE.txt


.. |test-status| image:: https://github.com/nedbat/coveragepy/actions/workflows/testsuite.yml/badge.svg?branch=master&event=push
    :target: https://github.com/nedbat/coveragepy/actions/workflows/testsuite.yml
    :alt: Test suite status
.. |quality-status| image:: https://github.com/nedbat/coveragepy/actions/workflows/quality.yml/badge.svg?branch=master&event=push
    :target: https://github.com/nedbat/coveragepy/actions/workflows/quality.yml
    :alt: Quality check status
.. |docs| image:: https://readthedocs.org/projects/coverage/badge/?version=latest&style=flat
    :target: https://coverage.readthedocs.io/en/7.9.1/
    :alt: Documentation
.. |kit| image:: https://img.shields.io/pypi/v/coverage
    :target: https://pypi.org/project/coverage/
    :alt: PyPI status
.. |versions| image:: https://img.shields.io/pypi/pyversions/coverage.svg?logo=python&logoColor=FBE072
    :target: https://pypi.org/project/coverage/
    :alt: Python versions supported
.. |license| image:: https://img.shields.io/pypi/l/coverage.svg
    :target: https://pypi.org/project/coverage/
    :alt: License
.. |metacov| image:: https://img.shields.io/endpoint?url=https://gist.githubusercontent.com/nedbat/8c6980f77988a327348f9b02bbaf67f5/raw/metacov.json
    :target: https://nedbat.github.io/coverage-reports/latest.html
    :alt: Coverage reports
.. |tidelift| image:: https://tidelift.com/badges/package/pypi/coverage
    :target: https://tidelift.com/subscription/pkg/pypi-coverage?utm_source=pypi-coverage&utm_medium=referral&utm_campaign=readme
    :alt: Tidelift
.. |stars| image:: https://img.shields.io/github/stars/nedbat/coveragepy.svg?logo=github&style=flat
    :target: https://github.com/nedbat/coveragepy/stargazers
    :alt: GitHub stars
.. |mastodon-nedbat| image:: https://img.shields.io/badge/dynamic/json?style=flat&labelColor=450657&logo=mastodon&logoColor=ffffff&label=@nedbat&query=followers_count&url=https%3A%2F%2Fhachyderm.io%2Fapi%2Fv1%2Faccounts%2Flookup%3Facct=nedbat
    :target: https://hachyderm.io/@nedbat
    :alt: nedbat on Mastodon
.. |mastodon-coveragepy| image:: https://img.shields.io/badge/dynamic/json?style=flat&labelColor=450657&logo=mastodon&logoColor=ffffff&label=@coveragepy&query=followers_count&url=https%3A%2F%2Fhachyderm.io%2Fapi%2Fv1%2Faccounts%2Flookup%3Facct=coveragepy
    :target: https://hachyderm.io/@coveragepy
    :alt: coveragepy on Mastodon
.. |bluesky-nedbat| image:: https://img.shields.io/badge/dynamic/json?style=flat&color=96a3b0&labelColor=3686f7&logo=icloud&logoColor=white&label=@nedbat&url=https%3A%2F%2Fpublic.api.bsky.app%2Fxrpc%2Fapp.bsky.actor.getProfile%3Factor=nedbat.com&query=followersCount
    :target: https://bsky.app/profile/nedbat.com
    :alt: nedbat on Bluesky
.. |sponsor| image:: https://img.shields.io/badge/%E2%9D%A4-Sponsor%20me-brightgreen?style=flat&logo=GitHub
    :target: https://github.com/sponsors/nedbat
    :alt: Sponsor me on GitHub
